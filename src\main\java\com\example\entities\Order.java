package com.example.entities;

import com.example.enums.OrderStatus;
import com.example.ids.OrderId;
import javax.persistence.*;
import com.example.converters.OrderStatusConverter;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * JPA Entity with composite ID using @IdClass annotation
 */
@Entity
@Table(name = "orders")
@IdClass(OrderId.class)
public class Order {

    @Id
    @Column(name = "order_number", length = 50)
    private String orderNumber;

    @Id
    @Convert(converter = OrderStatusConverter.class)
    @Column(name = "status", length = 10)
    private OrderStatus status;

    @Id
    @Column(name = "customer_id", length = 50)
    private String customerId;

    @Column(name = "order_date")
    private LocalDateTime orderDate;

    @Column(name = "total_amount", precision = 10, scale = 2)
    private BigDecimal totalAmount;

    @Column(name = "description", length = 500)
    private String description;

    // Default constructor
    public Order() {}

    // Constructor with composite ID
    public Order(String orderNumber, OrderStatus status, String customerId) {
        this.orderNumber = orderNumber;
        this.status = status;
        this.customerId = customerId;
        this.orderDate = LocalDateTime.now();
    }

    // Full constructor
    public Order(String orderNumber, OrderStatus status, String customerId, 
                 LocalDateTime orderDate, BigDecimal totalAmount, String description) {
        this.orderNumber = orderNumber;
        this.status = status;
        this.customerId = customerId;
        this.orderDate = orderDate;
        this.totalAmount = totalAmount;
        this.description = description;
    }

    // Getters and setters
    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public OrderStatus getStatus() {
        return status;
    }

    public void setStatus(OrderStatus status) {
        this.status = status;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public LocalDateTime getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(LocalDateTime orderDate) {
        this.orderDate = orderDate;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return "Order{" +
                "orderNumber='" + orderNumber + '\'' +
                ", status=" + status +
                ", customerId='" + customerId + '\'' +
                ", orderDate=" + orderDate +
                ", totalAmount=" + totalAmount +
                ", description='" + description + '\'' +
                '}';
    }
}
