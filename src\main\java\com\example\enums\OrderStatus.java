package com.example.enums;

/**
 * Custom enum with value attribute that will be stored in database
 * instead of the enum name. Uses a simpler approach with direct mapping.
 */
public enum OrderStatus {
    P("PENDING"),
    PR("PROCESSING"),
    S("SHIPPED"),
    D("DELIVERED"),
    C("CANCELLED");

    private final String displayName;

    OrderStatus(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }

    /**
     * Get the database value (which is the enum name itself)
     */
    public String getValue() {
        return this.name(); // Returns P, PR, S, D, C
    }

    /**
     * Get enum by value
     */
    public static OrderStatus fromValue(String value) {
        if (value == null) {
            throw new IllegalArgumentException("OrderStatus value cannot be null");
        }
        try {
            return OrderStatus.valueOf(value);
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Unknown OrderStatus value: " + value);
        }
    }
}
