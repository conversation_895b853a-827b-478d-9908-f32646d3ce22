package com.example.enums;

/**
 * Custom enum with value attribute that will be stored in database
 * instead of the enum name
 */
public enum OrderStatus {
    PENDING("P"),
    PROCESSING("PR"),
    SHIPPED("S"),
    DELIVERED("D"),
    CANCELLED("C");

    private final String value;

    OrderStatus(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    /**
     * Get enum by value
     */
    public static OrderStatus fromValue(String value) {
        for (OrderStatus status : OrderStatus.values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown OrderStatus value: " + value);
    }
}
