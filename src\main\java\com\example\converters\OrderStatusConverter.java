package com.example.converters;

import com.example.enums.OrderStatus;
import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/**
 * JPA converter to store enum value instead of enum name in database
 * Now works with the simplified enum approach
 */
@Converter(autoApply = true)
public class OrderStatusConverter implements AttributeConverter<OrderStatus, String> {

    @Override
    public String convertToDatabaseColumn(OrderStatus attribute) {
        if (attribute == null) {
            return null;
        }
        return attribute.name(); // Returns P, PR, S, D, C
    }

    @Override
    public OrderStatus convertToEntityAttribute(String dbData) {
        if (dbData == null) {
            return null;
        }
        return OrderStatus.valueOf(dbData);
    }
}
