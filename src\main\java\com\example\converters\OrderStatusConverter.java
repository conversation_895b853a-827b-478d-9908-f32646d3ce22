package com.example.converters;

import com.example.enums.OrderStatus;
import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/**
 * JPA converter to store enum value instead of enum name in database
 */
@Converter(autoApply = true)
public class OrderStatusConverter implements AttributeConverter<OrderStatus, String> {

    @Override
    public String convertToDatabaseColumn(OrderStatus attribute) {
        if (attribute == null) {
            return null;
        }
        return attribute.getValue();
    }

    @Override
    public OrderStatus convertToEntityAttribute(String dbData) {
        if (dbData == null) {
            return null;
        }
        return OrderStatus.fromValue(dbData);
    }
}
