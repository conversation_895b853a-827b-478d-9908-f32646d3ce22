version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: composite-id-postgres
    environment:
      POSTGRES_DB: composite_id_demo
      POSTGRES_USER: demo_user
      POSTGRES_PASSWORD: demo_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U demo_user -d composite_id_demo"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
