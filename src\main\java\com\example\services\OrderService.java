package com.example.services;

import com.example.entities.Order;
import com.example.enums.OrderStatus;
import com.example.ids.OrderId;
import com.example.repositories.OrderRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Service layer for Order operations
 */
@Service
@Transactional
public class OrderService {

    private final OrderRepository orderRepository;

    @Autowired
    public OrderService(OrderRepository orderRepository) {
        this.orderRepository = orderRepository;
    }

    /**
     * Create a new order
     */
    public Order createOrder(String orderNumber, OrderStatus status, String customerId, 
                           BigDecimal totalAmount, String description) {
        Order order = new Order(orderNumber, status, customerId, LocalDateTime.now(), totalAmount, description);
        return orderRepository.save(order);
    }

    /**
     * Find order by composite ID
     */
    @Transactional(readOnly = true)
    public Optional<Order> findById(OrderId orderId) {
        return orderRepository.findById(orderId);
    }

    /**
     * Find order by composite ID components
     */
    @Transactional(readOnly = true)
    public Optional<Order> findByCompositeKey(String orderNumber, OrderStatus status, String customerId) {
        OrderId orderId = new OrderId(orderNumber, status, customerId);
        return orderRepository.findById(orderId);
    }

    /**
     * Find all orders by customer
     */
    @Transactional(readOnly = true)
    public List<Order> findOrdersByCustomer(String customerId) {
        return orderRepository.findByCustomerId(customerId);
    }

    /**
     * Find all orders by status
     */
    @Transactional(readOnly = true)
    public List<Order> findOrdersByStatus(OrderStatus status) {
        return orderRepository.findByStatus(status);
    }

    /**
     * Update order status
     */
    public Optional<Order> updateOrderStatus(String orderNumber, OrderStatus currentStatus, 
                                           String customerId, OrderStatus newStatus) {
        OrderId currentId = new OrderId(orderNumber, currentStatus, customerId);
        Optional<Order> orderOpt = orderRepository.findById(currentId);
        
        if (orderOpt.isPresent()) {
            Order order = orderOpt.get();
            // Delete the old entity (since status is part of the primary key)
            orderRepository.delete(order);
            
            // Create new entity with updated status
            Order newOrder = new Order(orderNumber, newStatus, customerId, 
                                     order.getOrderDate(), order.getTotalAmount(), order.getDescription());
            return Optional.of(orderRepository.save(newOrder));
        }
        
        return Optional.empty();
    }

    /**
     * Delete order by composite ID
     */
    public boolean deleteOrder(OrderId orderId) {
        if (orderRepository.existsById(orderId)) {
            orderRepository.deleteById(orderId);
            return true;
        }
        return false;
    }

    /**
     * Get all orders
     */
    @Transactional(readOnly = true)
    public List<Order> getAllOrders() {
        return orderRepository.findAll();
    }

    /**
     * Check if order exists
     */
    @Transactional(readOnly = true)
    public boolean orderExists(String orderNumber, OrderStatus status, String customerId) {
        return orderRepository.existsByOrderNumberAndStatusAndCustomerId(orderNumber, status, customerId);
    }
}
