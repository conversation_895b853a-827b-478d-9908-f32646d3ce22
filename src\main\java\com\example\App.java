package com.example;

public class App {
    public static void main(String[] args) {
        while (true) {
            System.out.println("Hello World! é à ñ ± ÷ € ∑");
            try {
                Thread.sleep(2000); // Sleep for 2000 milliseconds (2 seconds)
            } catch (InterruptedException e) {
                System.err.println("Sleep interrupted: " + e.getMessage());
                break;
            }
        }
    }
}
