package com.example.integration;

import com.example.CompositeIdDemoApplication;
import com.example.entities.Order;
import com.example.enums.OrderStatus;
import com.example.ids.OrderId;
import com.example.repositories.OrderRepository;
import com.example.services.OrderService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.transaction.annotation.Transactional;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration tests for Order entity with composite ID
 */
@SpringBootTest(classes = CompositeIdDemoApplication.class)
@Testcontainers
@ActiveProfiles("test")
@Transactional
class OrderIntegrationTest {

    @Container
    static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:15-alpine")
            .withDatabaseName("composite_id_demo_test")
            .withUsername("demo_user")
            .withPassword("demo_password");

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);
    }

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private OrderService orderService;

    @BeforeEach
    void setUp() {
        orderRepository.deleteAll();
    }

    @Test
    void testCreateAndFindOrder() {
        // Create order
        Order order = orderService.createOrder(
                "ORD-001", 
                OrderStatus.PENDING, 
                "CUST-001",
                new BigDecimal("99.99"),
                "Test order"
        );

        assertNotNull(order);
        assertEquals("ORD-001", order.getOrderNumber());
        assertEquals(OrderStatus.PENDING, order.getStatus());
        assertEquals("CUST-001", order.getCustomerId());

        // Find by composite ID
        OrderId orderId = new OrderId("ORD-001", OrderStatus.PENDING, "CUST-001");
        Optional<Order> found = orderService.findById(orderId);

        assertTrue(found.isPresent());
        assertEquals(order.getOrderNumber(), found.get().getOrderNumber());
        assertEquals(order.getStatus(), found.get().getStatus());
        assertEquals(order.getCustomerId(), found.get().getCustomerId());
    }

    @Test
    void testEnumValueStoredInDatabase() {
        // Create order with PROCESSING status
        Order order = orderService.createOrder(
                "ORD-002", 
                OrderStatus.PROCESSING, 
                "CUST-002",
                new BigDecimal("149.99"),
                "Processing order"
        );

        // Verify the order was saved
        OrderId orderId = new OrderId("ORD-002", OrderStatus.PROCESSING, "CUST-002");
        Optional<Order> found = orderRepository.findById(orderId);

        assertTrue(found.isPresent());
        assertEquals(OrderStatus.PROCESSING, found.get().getStatus());
        
        // The enum value "PR" should be stored in database, not "PROCESSING"
        // This is verified by the fact that we can retrieve it successfully
    }

    @Test
    void testFindOrdersByStatus() {
        // Create orders with different statuses
        orderService.createOrder("ORD-003", OrderStatus.PENDING, "CUST-003", 
                                new BigDecimal("50.00"), "Pending order 1");
        orderService.createOrder("ORD-004", OrderStatus.PENDING, "CUST-004", 
                                new BigDecimal("75.00"), "Pending order 2");
        orderService.createOrder("ORD-005", OrderStatus.SHIPPED, "CUST-005", 
                                new BigDecimal("100.00"), "Shipped order");

        List<Order> pendingOrders = orderService.findOrdersByStatus(OrderStatus.PENDING);
        List<Order> shippedOrders = orderService.findOrdersByStatus(OrderStatus.SHIPPED);

        assertEquals(2, pendingOrders.size());
        assertEquals(1, shippedOrders.size());
    }

    @Test
    void testFindOrdersByCustomer() {
        // Create orders for same customer
        orderService.createOrder("ORD-006", OrderStatus.PENDING, "CUST-006", 
                                new BigDecimal("25.00"), "Order 1");
        orderService.createOrder("ORD-007", OrderStatus.PROCESSING, "CUST-006", 
                                new BigDecimal("35.00"), "Order 2");
        orderService.createOrder("ORD-008", OrderStatus.SHIPPED, "CUST-007", 
                                new BigDecimal("45.00"), "Order 3");

        List<Order> customer006Orders = orderService.findOrdersByCustomer("CUST-006");
        List<Order> customer007Orders = orderService.findOrdersByCustomer("CUST-007");

        assertEquals(2, customer006Orders.size());
        assertEquals(1, customer007Orders.size());
    }

    @Test
    void testUpdateOrderStatus() {
        // Create order
        orderService.createOrder("ORD-009", OrderStatus.PENDING, "CUST-009", 
                                new BigDecimal("60.00"), "Order to update");

        // Update status
        Optional<Order> updated = orderService.updateOrderStatus(
                "ORD-009", OrderStatus.PENDING, "CUST-009", OrderStatus.PROCESSING);

        assertTrue(updated.isPresent());
        assertEquals(OrderStatus.PROCESSING, updated.get().getStatus());

        // Verify old record is gone and new record exists
        OrderId oldId = new OrderId("ORD-009", OrderStatus.PENDING, "CUST-009");
        OrderId newId = new OrderId("ORD-009", OrderStatus.PROCESSING, "CUST-009");

        assertFalse(orderRepository.existsById(oldId));
        assertTrue(orderRepository.existsById(newId));
    }

    @Test
    void testDeleteOrder() {
        // Create order
        orderService.createOrder("ORD-010", OrderStatus.CANCELLED, "CUST-010", 
                                new BigDecimal("80.00"), "Order to delete");

        OrderId orderId = new OrderId("ORD-010", OrderStatus.CANCELLED, "CUST-010");
        assertTrue(orderService.orderExists("ORD-010", OrderStatus.CANCELLED, "CUST-010"));

        // Delete order
        boolean deleted = orderService.deleteOrder(orderId);
        assertTrue(deleted);
        assertFalse(orderService.orderExists("ORD-010", OrderStatus.CANCELLED, "CUST-010"));
    }

    @Test
    void testCompositeKeyUniqueness() {
        // Create order
        orderService.createOrder("ORD-011", OrderStatus.PENDING, "CUST-011",
                                new BigDecimal("90.00"), "First order");

        // Try to create another order with same composite key - should fail
        // Note: This will fail at the database level due to primary key constraint
        assertThrows(Exception.class, () -> {
            orderService.createOrder("ORD-011", OrderStatus.PENDING, "CUST-011",
                                    new BigDecimal("95.00"), "Duplicate order");
            // Force flush to trigger the constraint violation
            orderRepository.flush();
        });
    }

    @Test
    void testDifferentStatusesSameOrderNumber() {
        // Same order number and customer but different status should be allowed
        orderService.createOrder("ORD-012", OrderStatus.PENDING, "CUST-012", 
                                new BigDecimal("100.00"), "Pending version");
        orderService.createOrder("ORD-012", OrderStatus.PROCESSING, "CUST-012", 
                                new BigDecimal("100.00"), "Processing version");

        List<Order> allOrders = orderService.getAllOrders();
        long count = allOrders.stream()
                .filter(o -> "ORD-012".equals(o.getOrderNumber()) && "CUST-012".equals(o.getCustomerId()))
                .count();

        assertEquals(2, count);
    }
}
