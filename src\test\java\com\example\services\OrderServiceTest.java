package com.example.services;

import com.example.entities.Order;
import com.example.enums.OrderStatus;
import com.example.ids.OrderId;
import com.example.repositories.OrderRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Unit tests for OrderService
 */
@ExtendWith(MockitoExtension.class)
class OrderServiceTest {

    @Mock
    private OrderRepository orderRepository;

    @InjectMocks
    private OrderService orderService;

    private Order testOrder;
    private OrderId testOrderId;

    @BeforeEach
    void setUp() {
        testOrderId = new OrderId("ORD-001", OrderStatus.P, "CUST-001");
        testOrder = new Order("ORD-001", OrderStatus.P, "CUST-001",
                             LocalDateTime.now(), new BigDecimal("99.99"), "Test order");
    }

    @Test
    void testCreateOrder() {
        when(orderRepository.save(any(Order.class))).thenReturn(testOrder);

        Order result = orderService.createOrder("ORD-001", OrderStatus.P, "CUST-001",
                                               new BigDecimal("99.99"), "Test order");

        assertNotNull(result);
        assertEquals("ORD-001", result.getOrderNumber());
        assertEquals(OrderStatus.P, result.getStatus());
        assertEquals("CUST-001", result.getCustomerId());
        verify(orderRepository).save(any(Order.class));
    }

    @Test
    void testFindById() {
        when(orderRepository.findById(testOrderId)).thenReturn(Optional.of(testOrder));

        Optional<Order> result = orderService.findById(testOrderId);

        assertTrue(result.isPresent());
        assertEquals(testOrder, result.get());
        verify(orderRepository).findById(testOrderId);
    }

    @Test
    void testFindByCompositeKey() {
        when(orderRepository.findById(any(OrderId.class))).thenReturn(Optional.of(testOrder));

        Optional<Order> result = orderService.findByCompositeKey("ORD-001", OrderStatus.P, "CUST-001");

        assertTrue(result.isPresent());
        assertEquals(testOrder, result.get());
        verify(orderRepository).findById(any(OrderId.class));
    }

    @Test
    void testFindOrdersByCustomer() {
        List<Order> expectedOrders = Arrays.asList(testOrder);
        when(orderRepository.findByCustomerId("CUST-001")).thenReturn(expectedOrders);

        List<Order> result = orderService.findOrdersByCustomer("CUST-001");

        assertEquals(1, result.size());
        assertEquals(testOrder, result.get(0));
        verify(orderRepository).findByCustomerId("CUST-001");
    }

    @Test
    void testFindOrdersByStatus() {
        List<Order> expectedOrders = Arrays.asList(testOrder);
        when(orderRepository.findByStatus(OrderStatus.P)).thenReturn(expectedOrders);

        List<Order> result = orderService.findOrdersByStatus(OrderStatus.P);

        assertEquals(1, result.size());
        assertEquals(testOrder, result.get(0));
        verify(orderRepository).findByStatus(OrderStatus.P);
    }

    @Test
    void testUpdateOrderStatus() {
        when(orderRepository.findById(any(OrderId.class))).thenReturn(Optional.of(testOrder));
        when(orderRepository.save(any(Order.class))).thenReturn(testOrder);

        Optional<Order> result = orderService.updateOrderStatus("ORD-001", OrderStatus.P,
                                                               "CUST-001", OrderStatus.PR);

        assertTrue(result.isPresent());
        verify(orderRepository).findById(any(OrderId.class));
        verify(orderRepository).delete(testOrder);
        verify(orderRepository).save(any(Order.class));
    }

    @Test
    void testUpdateOrderStatusNotFound() {
        when(orderRepository.findById(any(OrderId.class))).thenReturn(Optional.empty());

        Optional<Order> result = orderService.updateOrderStatus("ORD-001", OrderStatus.P,
                                                               "CUST-001", OrderStatus.PR);

        assertFalse(result.isPresent());
        verify(orderRepository).findById(any(OrderId.class));
        verify(orderRepository, never()).delete(any());
        verify(orderRepository, never()).save(any());
    }

    @Test
    void testDeleteOrder() {
        when(orderRepository.existsById(testOrderId)).thenReturn(true);

        boolean result = orderService.deleteOrder(testOrderId);

        assertTrue(result);
        verify(orderRepository).existsById(testOrderId);
        verify(orderRepository).deleteById(testOrderId);
    }

    @Test
    void testDeleteOrderNotFound() {
        when(orderRepository.existsById(testOrderId)).thenReturn(false);

        boolean result = orderService.deleteOrder(testOrderId);

        assertFalse(result);
        verify(orderRepository).existsById(testOrderId);
        verify(orderRepository, never()).deleteById(any());
    }

    @Test
    void testGetAllOrders() {
        List<Order> expectedOrders = Arrays.asList(testOrder);
        when(orderRepository.findAll()).thenReturn(expectedOrders);

        List<Order> result = orderService.getAllOrders();

        assertEquals(1, result.size());
        assertEquals(testOrder, result.get(0));
        verify(orderRepository).findAll();
    }

    @Test
    void testOrderExists() {
        when(orderRepository.existsByOrderNumberAndStatusAndCustomerId("ORD-001", OrderStatus.P, "CUST-001"))
                .thenReturn(true);

        boolean result = orderService.orderExists("ORD-001", OrderStatus.P, "CUST-001");

        assertTrue(result);
        verify(orderRepository).existsByOrderNumberAndStatusAndCustomerId("ORD-001", OrderStatus.P, "CUST-001");
    }
}
