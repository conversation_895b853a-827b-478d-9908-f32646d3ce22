package com.example.demo;

import com.example.converters.OrderStatusConverter;
import com.example.enums.OrderStatus;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Simple test to verify the converter works correctly
 */
class SimpleConverterTest {

    @Test
    void testConverterWorksCorrectly() {
        OrderStatusConverter converter = new OrderStatusConverter();
        
        // Test conversion to database column
        assertEquals("P", converter.convertToDatabaseColumn(OrderStatus.P));
        assertEquals("PR", converter.convertToDatabaseColumn(OrderStatus.PR));
        assertEquals("S", converter.convertToDatabaseColumn(OrderStatus.S));
        assertEquals("D", converter.convertToDatabaseColumn(OrderStatus.D));
        assertEquals("C", converter.convertToDatabaseColumn(OrderStatus.C));

        // Test conversion from database column
        assertEquals(OrderStatus.P, converter.convertToEntityAttribute("P"));
        assertEquals(OrderStatus.PR, converter.convertToEntityAttribute("PR"));
        assertEquals(OrderStatus.S, converter.convertToEntityAttribute("S"));
        assertEquals(OrderStatus.D, converter.convertToEntityAttribute("D"));
        assertEquals(OrderStatus.C, converter.convertToEntityAttribute("C"));

        System.out.println("✅ Converter is working correctly!");
        System.out.println("✅ P -> " + converter.convertToDatabaseColumn(OrderStatus.P));
        System.out.println("✅ PR -> " + converter.convertToDatabaseColumn(OrderStatus.PR));
        System.out.println("✅ S -> " + converter.convertToDatabaseColumn(OrderStatus.S));
        System.out.println("✅ D -> " + converter.convertToDatabaseColumn(OrderStatus.D));
        System.out.println("✅ C -> " + converter.convertToDatabaseColumn(OrderStatus.C));
    }
}
