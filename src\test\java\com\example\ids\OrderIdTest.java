package com.example.ids;

import com.example.enums.OrderStatus;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for OrderId composite key
 */
class OrderIdTest {

    @Test
    void testConstructorAndGetters() {
        OrderId orderId = new OrderId("ORD-001", OrderStatus.P, "CUST-001");

        assertEquals("ORD-001", orderId.getOrderNumber());
        assertEquals(OrderStatus.P, orderId.getStatus());
        assertEquals("CUST-001", orderId.getCustomerId());
    }

    @Test
    void testDefaultConstructor() {
        OrderId orderId = new OrderId();
        assertNull(orderId.getOrderNumber());
        assertNull(orderId.getStatus());
        assertNull(orderId.getCustomerId());
    }

    @Test
    void testSetters() {
        OrderId orderId = new OrderId();
        orderId.setOrderNumber("ORD-002");
        orderId.setStatus(OrderStatus.PR);
        orderId.setCustomerId("CUST-002");

        assertEquals("ORD-002", orderId.getOrderNumber());
        assertEquals(OrderStatus.PR, orderId.getStatus());
        assertEquals("CUST-002", orderId.getCustomerId());
    }

    @Test
    void testEquals() {
        OrderId orderId1 = new OrderId("ORD-001", OrderStatus.P, "CUST-001");
        OrderId orderId2 = new OrderId("ORD-001", OrderStatus.P, "CUST-001");
        OrderId orderId3 = new OrderId("ORD-002", OrderStatus.P, "CUST-001");

        assertEquals(orderId1, orderId2);
        assertNotEquals(orderId1, orderId3);
        assertNotEquals(orderId1, null);
        assertNotEquals(orderId1, "not an OrderId");
        assertEquals(orderId1, orderId1); // reflexive
    }

    @Test
    void testHashCode() {
        OrderId orderId1 = new OrderId("ORD-001", OrderStatus.P, "CUST-001");
        OrderId orderId2 = new OrderId("ORD-001", OrderStatus.P, "CUST-001");
        OrderId orderId3 = new OrderId("ORD-002", OrderStatus.P, "CUST-001");

        assertEquals(orderId1.hashCode(), orderId2.hashCode());
        assertNotEquals(orderId1.hashCode(), orderId3.hashCode());
    }

    @Test
    void testToString() {
        OrderId orderId = new OrderId("ORD-001", OrderStatus.P, "CUST-001");
        String toString = orderId.toString();

        assertTrue(toString.contains("ORD-001"));
        assertTrue(toString.contains("P"));
        assertTrue(toString.contains("CUST-001"));
    }

    @Test
    void testEqualsWithNullFields() {
        OrderId orderId1 = new OrderId(null, null, null);
        OrderId orderId2 = new OrderId(null, null, null);
        OrderId orderId3 = new OrderId("ORD-001", null, null);

        assertEquals(orderId1, orderId2);
        assertNotEquals(orderId1, orderId3);
    }
}
