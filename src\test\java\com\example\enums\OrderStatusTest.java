package com.example.enums;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for OrderStatus enum
 */
class OrderStatusTest {

    @Test
    void testEnumValues() {
        assertEquals("P", OrderStatus.PENDING.getValue());
        assertEquals("PR", OrderStatus.PROCESSING.getValue());
        assertEquals("S", OrderStatus.SHIPPED.getValue());
        assertEquals("D", OrderStatus.DELIVERED.getValue());
        assertEquals("C", OrderStatus.CANCELLED.getValue());
    }

    @Test
    void testFromValue() {
        assertEquals(OrderStatus.PENDING, OrderStatus.fromValue("P"));
        assertEquals(OrderStatus.PROCESSING, OrderStatus.fromValue("PR"));
        assertEquals(OrderStatus.SHIPPED, OrderStatus.fromValue("S"));
        assertEquals(OrderStatus.DELIVERED, OrderStatus.fromValue("D"));
        assertEquals(OrderStatus.CANCELLED, OrderStatus.fromValue("C"));
    }

    @Test
    void testFromValueInvalid() {
        assertThrows(IllegalArgumentException.class, () -> {
            OrderStatus.fromValue("INVALID");
        });
    }

    @Test
    void testFromValueNull() {
        assertThrows(IllegalArgumentException.class, () -> {
            OrderStatus.fromValue(null);
        });
    }

    @Test
    void testAllEnumValuesHaveUniqueValues() {
        OrderStatus[] statuses = OrderStatus.values();
        for (int i = 0; i < statuses.length; i++) {
            for (int j = i + 1; j < statuses.length; j++) {
                assertNotEquals(statuses[i].getValue(), statuses[j].getValue(),
                    "Enum values should be unique: " + statuses[i] + " and " + statuses[j]);
            }
        }
    }
}
