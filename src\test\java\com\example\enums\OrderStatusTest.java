package com.example.enums;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for OrderStatus enum
 */
class OrderStatusTest {

    @Test
    void testEnumValues() {
        assertEquals("P", OrderStatus.P.getValue());
        assertEquals("PR", OrderStatus.PR.getValue());
        assertEquals("S", OrderStatus.S.getValue());
        assertEquals("D", OrderStatus.D.getValue());
        assertEquals("C", OrderStatus.C.getValue());
    }

    @Test
    void testFromValue() {
        assertEquals(OrderStatus.P, OrderStatus.fromValue("P"));
        assertEquals(OrderStatus.PR, OrderStatus.fromValue("PR"));
        assertEquals(OrderStatus.S, OrderStatus.fromValue("S"));
        assertEquals(OrderStatus.D, OrderStatus.fromValue("D"));
        assertEquals(OrderStatus.C, OrderStatus.fromValue("C"));
    }

    @Test
    void testFromValueInvalid() {
        assertThrows(IllegalArgumentException.class, () -> {
            OrderStatus.fromValue("INVALID");
        });
    }

    @Test
    void testFromValueNull() {
        assertThrows(IllegalArgumentException.class, () -> {
            OrderStatus.fromValue(null);
        });
    }

    @Test
    void testAllEnumValuesHaveUniqueValues() {
        OrderStatus[] statuses = OrderStatus.values();
        for (int i = 0; i < statuses.length; i++) {
            for (int j = i + 1; j < statuses.length; j++) {
                assertNotEquals(statuses[i].getValue(), statuses[j].getValue(),
                    "Enum values should be unique: " + statuses[i] + " and " + statuses[j]);
            }
        }
    }

    @Test
    void testDisplayNames() {
        assertEquals("PENDING", OrderStatus.P.getDisplayName());
        assertEquals("PROCESSING", OrderStatus.PR.getDisplayName());
        assertEquals("SHIPPED", OrderStatus.S.getDisplayName());
        assertEquals("DELIVERED", OrderStatus.D.getDisplayName());
        assertEquals("CANCELLED", OrderStatus.C.getDisplayName());
    }
}
