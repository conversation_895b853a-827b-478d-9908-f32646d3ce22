package com.example.converters;

import com.example.enums.OrderStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for OrderStatusConverter
 */
class OrderStatusConverterTest {

    private OrderStatusConverter converter;

    @BeforeEach
    void setUp() {
        converter = new OrderStatusConverter();
    }

    @Test
    void testConvertToDatabaseColumn() {
        assertEquals("P", converter.convertToDatabaseColumn(OrderStatus.P));
        assertEquals("PR", converter.convertToDatabaseColumn(OrderStatus.PR));
        assertEquals("S", converter.convertToDatabaseColumn(OrderStatus.S));
        assertEquals("D", converter.convertToDatabaseColumn(OrderStatus.D));
        assertEquals("C", converter.convertToDatabaseColumn(OrderStatus.C));
    }

    @Test
    void testConvertToDatabaseColumnNull() {
        assertNull(converter.convertToDatabaseColumn(null));
    }

    @Test
    void testConvertToEntityAttribute() {
        assertEquals(OrderStatus.P, converter.convertToEntityAttribute("P"));
        assertEquals(OrderStatus.PR, converter.convertToEntityAttribute("PR"));
        assertEquals(OrderStatus.S, converter.convertToEntityAttribute("S"));
        assertEquals(OrderStatus.D, converter.convertToEntityAttribute("D"));
        assertEquals(OrderStatus.C, converter.convertToEntityAttribute("C"));
    }

    @Test
    void testConvertToEntityAttributeNull() {
        assertNull(converter.convertToEntityAttribute(null));
    }

    @Test
    void testConvertToEntityAttributeInvalid() {
        assertThrows(IllegalArgumentException.class, () -> {
            converter.convertToEntityAttribute("INVALID");
        });
    }

    @Test
    void testRoundTrip() {
        for (OrderStatus status : OrderStatus.values()) {
            String dbValue = converter.convertToDatabaseColumn(status);
            OrderStatus convertedBack = converter.convertToEntityAttribute(dbValue);
            assertEquals(status, convertedBack);
        }
    }
}
