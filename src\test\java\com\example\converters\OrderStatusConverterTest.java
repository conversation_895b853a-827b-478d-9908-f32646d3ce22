package com.example.converters;

import com.example.enums.OrderStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for OrderStatusConverter
 */
class OrderStatusConverterTest {

    private OrderStatusConverter converter;

    @BeforeEach
    void setUp() {
        converter = new OrderStatusConverter();
    }

    @Test
    void testConvertToDatabaseColumn() {
        assertEquals("P", converter.convertToDatabaseColumn(OrderStatus.PENDING));
        assertEquals("PR", converter.convertToDatabaseColumn(OrderStatus.PROCESSING));
        assertEquals("S", converter.convertToDatabaseColumn(OrderStatus.SHIPPED));
        assertEquals("D", converter.convertToDatabaseColumn(OrderStatus.DELIVERED));
        assertEquals("C", converter.convertToDatabaseColumn(OrderStatus.CANCELLED));
    }

    @Test
    void testConvertToDatabaseColumnNull() {
        assertNull(converter.convertToDatabaseColumn(null));
    }

    @Test
    void testConvertToEntityAttribute() {
        assertEquals(OrderStatus.PENDING, converter.convertToEntityAttribute("P"));
        assertEquals(OrderStatus.PROCESSING, converter.convertToEntityAttribute("PR"));
        assertEquals(OrderStatus.SHIPPED, converter.convertToEntityAttribute("S"));
        assertEquals(OrderStatus.DELIVERED, converter.convertToEntityAttribute("D"));
        assertEquals(OrderStatus.CANCELLED, converter.convertToEntityAttribute("C"));
    }

    @Test
    void testConvertToEntityAttributeNull() {
        assertNull(converter.convertToEntityAttribute(null));
    }

    @Test
    void testConvertToEntityAttributeInvalid() {
        assertThrows(IllegalArgumentException.class, () -> {
            converter.convertToEntityAttribute("INVALID");
        });
    }

    @Test
    void testRoundTrip() {
        for (OrderStatus status : OrderStatus.values()) {
            String dbValue = converter.convertToDatabaseColumn(status);
            OrderStatus convertedBack = converter.convertToEntityAttribute(dbValue);
            assertEquals(status, convertedBack);
        }
    }
}
