package com.example.ids;

import com.example.enums.OrderStatus;
import java.io.Serializable;
import java.util.Objects;

/**
 * Composite ID class for Order entity
 * Must implement Serializable and override equals() and hashCode()
 */
public class OrderId implements Serializable {
    
    private String orderNumber;
    private OrderStatus status;
    private String customerId;

    // Default constructor required
    public OrderId() {}

    public OrderId(String orderNumber, OrderStatus status, String customerId) {
        this.orderNumber = orderNumber;
        this.status = status;
        this.customerId = customerId;
    }

    // Getters and setters
    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public OrderStatus getStatus() {
        return status;
    }

    public void setStatus(OrderStatus status) {
        this.status = status;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        OrderId orderId = (OrderId) o;
        return Objects.equals(orderNumber, orderId.orderNumber) &&
               status == orderId.status &&
               Objects.equals(customerId, orderId.customerId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(orderNumber, status, customerId);
    }

    @Override
    public String toString() {
        return "OrderId{" +
                "orderNumber='" + orderNumber + '\'' +
                ", status=" + status +
                ", customerId='" + customerId + '\'' +
                '}';
    }
}
