package com.example.repositories;

import com.example.entities.Order;
import com.example.enums.OrderStatus;
import com.example.ids.OrderId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repository for Order entity with composite ID
 */
@Repository
public interface OrderRepository extends JpaRepository<Order, OrderId> {

    /**
     * Find orders by customer ID
     */
    List<Order> findByCustomerId(String customerId);

    /**
     * Find orders by status
     */
    List<Order> findByStatus(OrderStatus status);

    /**
     * Find orders by order number (part of composite key)
     */
    List<Order> findByOrderNumber(String orderNumber);

    /**
     * Find orders by customer ID and status
     */
    List<Order> findByCustomerIdAndStatus(String customerId, OrderStatus status);

    /**
     * Custom query to find orders by status value (stored in database)
     */
    @Query("SELECT o FROM Order o WHERE o.status = :status")
    List<Order> findOrdersByStatus(@Param("status") OrderStatus status);

    /**
     * Check if order exists by composite key components
     */
    boolean existsByOrderNumberAndStatusAndCustomerId(String orderNumber, OrderStatus status, String customerId);
}
